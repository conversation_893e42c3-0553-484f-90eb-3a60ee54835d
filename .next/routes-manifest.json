{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "regex": "^(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/blog/[id]", "regex": "^/blog/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/blog/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/404", "regex": "^/404(?:/)?$", "routeKeys": {}, "namedRegex": "^/404(?:/)?$"}, {"page": "/blog", "regex": "^/blog(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog(?:/)?$"}, {"page": "/privacy-policy", "regex": "^/privacy\\-policy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy\\-policy(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/terms-of-service", "regex": "^/terms\\-of\\-service(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms\\-of\\-service(?:/)?$"}], "dataRoutes": [{"page": "/", "dataRouteRegex": "^/_next/data/cMishglSX3OOb14paXIPh/index.json$"}, {"page": "/blog", "dataRouteRegex": "^/_next/data/cMishglSX3OOb14paXIPh/blog.json$"}, {"page": "/blog/[id]", "routeKeys": {"nxtPid": "nxtPid"}, "dataRouteRegex": "^/_next/data/cMishglSX3OOb14paXIPh/blog/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cMishglSX3OOb14paXIPh/blog/(?<nxtPid>[^/]+?)\\.json$"}, {"page": "/privacy-policy", "dataRouteRegex": "^/_next/data/cMishglSX3OOb14paXIPh/privacy-policy.json$"}, {"page": "/profile", "dataRouteRegex": "^/_next/data/cMishglSX3OOb14paXIPh/profile.json$"}, {"page": "/terms-of-service", "dataRouteRegex": "^/_next/data/cMishglSX3OOb14paXIPh/terms-of-service.json$"}], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}